package matcher

import (
	"encoding/json"
	"io/ioutil"
	"log"
	"strings"

	"net-interceptor/parser"
)

type Rules struct {
	Keywords []string `json:"keywords"`
}

// MatchResult 匹配结果
type MatchResult struct {
	Matched        bool   // 是否匹配
	MatchedField   string // 匹配的字段
	MatchedKeyword string // 匹配的关键词
}

func LoadRules(path string) (*Rules, error) {
	data, err := ioutil.ReadFile(path)
	if err != nil {
		return nil, err
	}
	var r Rules
	if err := json.Unmarshal(data, &r); err != nil {
		return nil, err
	}
	log.Printf("规则已加载：%v", r.Keywords)
	return &r, nil
}

// Match 保持向后兼容的简单匹配函数
func Match(payload string, rules *Rules) bool {
	if len(payload) == 0 {
		return false
	}

	for _, kw := range rules.Keywords {
		if strings.Contains(payload, kw) {
			log.Printf("🎯 匹配成功！关键词: %s", kw)
			return true
		}
	}
	return false
}

// MatchPacket 增强的数据包匹配函数
func MatchPacket(packet *parser.Packet, rules *Rules) *MatchResult {
	result := &MatchResult{
		Matched: false,
	}

	if packet == nil || rules == nil {
		return result
	}

	// 检查HTTP Host头部
	if packet.HTTPHost != "" {
		for _, kw := range rules.Keywords {
			if strings.Contains(packet.HTTPHost, kw) {
				result.Matched = true
				result.MatchedField = "HTTPHost"
				result.MatchedKeyword = kw
				log.Printf("🎯 在HTTP Host中匹配成功！Host: %s, 关键词: %s", packet.HTTPHost, kw)
				return result
			}
		}
	}

	// 检查TLS SNI
	if packet.TLSServerName != "" {
		for _, kw := range rules.Keywords {
			if strings.Contains(packet.TLSServerName, kw) {
				result.Matched = true
				result.MatchedField = "TLSServerName"
				result.MatchedKeyword = kw
				log.Printf("🎯 在TLS SNI中匹配成功！SNI: %s, 关键词: %s", packet.TLSServerName, kw)
				return result
			}
		}
	}

	// 检查DNS查询
	for _, query := range packet.DNSQueries {
		for _, kw := range rules.Keywords {
			if strings.Contains(query, kw) {
				result.Matched = true
				result.MatchedField = "DNSQuery"
				result.MatchedKeyword = kw
				log.Printf("🎯 在DNS查询中匹配成功！查询: %s, 关键词: %s", query, kw)
				return result
			}
		}
	}

	// 检查HTTP路径
	if packet.HTTPPath != "" {
		for _, kw := range rules.Keywords {
			if strings.Contains(packet.HTTPPath, kw) {
				result.Matched = true
				result.MatchedField = "HTTPPath"
				result.MatchedKeyword = kw
				log.Printf("🎯 在HTTP路径中匹配成功！路径: %s, 关键词: %s", packet.HTTPPath, kw)
				return result
			}
		}
	}

	// 检查HTTP头部
	for headerName, headerValue := range packet.HTTPHeaders {
		for _, kw := range rules.Keywords {
			if strings.Contains(headerValue, kw) {
				result.Matched = true
				result.MatchedField = "HTTPHeader:" + headerName
				result.MatchedKeyword = kw
				log.Printf("🎯 在HTTP头部中匹配成功！%s: %s, 关键词: %s", headerName, headerValue, kw)
				return result
			}
		}
	}

	// 检查原始Payload
	if packet.Payload != "" && packet.Payload != "[binary]" {
		for _, kw := range rules.Keywords {
			if strings.Contains(packet.Payload, kw) {
				result.Matched = true
				result.MatchedField = "Payload"
				result.MatchedKeyword = kw
				log.Printf("🎯 在Payload中匹配成功！关键词: %s", kw)
				return result
			}
		}
	}

	return result
}
