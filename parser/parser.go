package parser

import (
	"log"
	"strings"

	"net-interceptor/interceptor"
)

type Packet struct {
	SrcIP         string
	DstIP         string
	SrcPort       uint16
	DstPort       uint16
	Payload       string
	HTTPMethod    string            // HTTP方法 (GET, POST等)
	HTTPHost      string            // HTTP Host头部
	HTTPPath      string            // HTTP路径
	HTTPHeaders   map[string]string // HTTP头部
	Protocol      string            // 协议类型 (HTTP, HTTPS, DNS, TCP, UDP等)
	TLSServerName string            // TLS SNI中的服务器名称
	DNSQueries    []string          // DNS查询的域名列表
}

func Parse(meta *interceptor.PacketMetadata, raw []byte) *Packet {
	packet := &Packet{
		SrcIP:       meta.SrcIP,
		DstIP:       meta.DstIP,
		SrcPort:     meta.SrcPort,
		DstPort:     meta.DstPort,
		HTTPHeaders: make(map[string]string),
		DNSQueries:  make([]string, 0),
		Protocol:    determineProtocol(raw, meta.DstPort),
	}

	// 根据协议类型解析payload
	switch packet.Protocol {
	case "TCP":
		payload := extractTCPPayload(raw)
		packet.Payload = payload
		if len(payload) > 0 && payload != "[binary]" {
			parseHTTP(packet, payload)
		}
	case "HTTPS":
		payload := extractTCPPayload(raw)
		packet.Payload = payload
		if len(payload) > 0 && payload != "[binary]" {
			parseTLS(packet, payload)
		}
	case "DNS":
		payload := extractUDPPayload(raw)
		packet.Payload = payload
		if len(payload) > 0 && payload != "[binary]" {
			parseDNS(packet, payload)
		}
	case "UDP":
		payload := extractUDPPayload(raw)
		packet.Payload = payload
	default:
		packet.Payload = extractPayload(raw)
	}

	return packet
}

// extractTCPPayload 从原始数据包中提取TCP payload
func extractTCPPayload(raw []byte) string {
	if len(raw) < 20 {
		return ""
	}

	// 解析IP头部长度
	ipHeaderLen := int(raw[0]&0x0F) * 4
	if ipHeaderLen < 20 || len(raw) < ipHeaderLen {
		return ""
	}

	// 检查是否是TCP协议 (协议号6)
	if raw[9] != 6 {
		return ""
	}

	// 检查TCP头部
	if len(raw) < ipHeaderLen+20 {
		return ""
	}

	// 解析TCP头部长度
	tcpHeaderLen := int(raw[ipHeaderLen+12]>>4) * 4
	if tcpHeaderLen < 20 {
		return ""
	}

	// 计算payload起始位置
	payloadStart := ipHeaderLen + tcpHeaderLen
	if payloadStart >= len(raw) {
		return ""
	}

	// 提取TCP payload
	payload := raw[payloadStart:]
	if len(payload) == 0 {
		return ""
	}

	// 转换为字符串
	return extractPayload(payload)
}

func extractPayload(raw []byte) string {
	if len(raw) == 0 {
		return ""
	}
	// 尝试只提取文本内容
	if isPrintable(raw) {
		return string(raw)
	}
	return "[binary]"
}

func isPrintable(data []byte) bool {
	for _, b := range data {
		if b < 32 && b != 9 && b != 10 && b != 13 {
			return false
		}
	}
	return true
}

// determineProtocol 根据端口和内容判断协议类型
func determineProtocol(raw []byte, dstPort uint16) string {
	if len(raw) < 20 {
		return "UNKNOWN"
	}

	protocol := raw[9] // IP协议字段

	if protocol == 6 { // TCP
		switch dstPort {
		case 80:
			return "HTTP"
		case 443:
			return "HTTPS"
		default:
			return "TCP"
		}
	} else if protocol == 17 { // UDP
		switch dstPort {
		case 53:
			return "DNS"
		default:
			return "UDP"
		}
	}

	return "UNKNOWN"
}

// extractUDPPayload 从原始数据包中提取UDP payload
func extractUDPPayload(raw []byte) string {
	if len(raw) < 20 {
		return ""
	}

	// 解析IP头部长度
	ipHeaderLen := int(raw[0]&0x0F) * 4
	if ipHeaderLen < 20 || len(raw) < ipHeaderLen {
		return ""
	}

	// 检查是否是UDP协议 (协议号17)
	if raw[9] != 17 {
		return ""
	}

	// 检查UDP头部（8字节）
	if len(raw) < ipHeaderLen+8 {
		return ""
	}

	// 计算payload起始位置
	payloadStart := ipHeaderLen + 8 // UDP头部固定8字节
	if payloadStart >= len(raw) {
		return ""
	}

	// 提取UDP payload
	payload := raw[payloadStart:]
	if len(payload) == 0 {
		return ""
	}

	// 转换为字符串
	return extractPayload(payload)
}

// parseHTTP 解析HTTP请求和响应
func parseHTTP(packet *Packet, payload string) {
	lines := strings.Split(payload, "\r\n")
	if len(lines) == 0 {
		return
	}

	firstLine := lines[0]

	// 检查是否是HTTP请求
	if isHTTPRequest(firstLine) {
		packet.Protocol = "HTTP"
		parseHTTPRequest(packet, lines)
	} else if isHTTPResponse(firstLine) {
		packet.Protocol = "HTTP"
		parseHTTPResponse(packet, lines)
	} else {
		// 检查是否包含HTTP头部
		if containsHTTPHeaders(payload) {
			packet.Protocol = "HTTP"
			parseHTTPHeaders(packet, lines)
		}
	}
}

// isHTTPRequest 检查是否是HTTP请求
func isHTTPRequest(line string) bool {
	methods := []string{"GET", "POST", "PUT", "DELETE", "HEAD", "OPTIONS", "PATCH", "TRACE", "CONNECT"}
	for _, method := range methods {
		if strings.HasPrefix(line, method+" ") {
			return true
		}
	}
	return false
}

// isHTTPResponse 检查是否是HTTP响应
func isHTTPResponse(line string) bool {
	return strings.HasPrefix(line, "HTTP/")
}

// containsHTTPHeaders 检查是否包含HTTP头部
func containsHTTPHeaders(payload string) bool {
	// 查找常见的HTTP头部
	headers := []string{"Host:", "User-Agent:", "Content-Type:", "Content-Length:", "Accept:", "Authorization:"}
	for _, header := range headers {
		if strings.Contains(payload, header) {
			return true
		}
	}
	return false
}

// parseHTTPRequest 解析HTTP请求
func parseHTTPRequest(packet *Packet, lines []string) {
	if len(lines) == 0 {
		return
	}

	// 解析请求行: GET /path HTTP/1.1
	requestLine := lines[0]
	parts := strings.Fields(requestLine)
	if len(parts) >= 2 {
		packet.HTTPMethod = parts[0]
		packet.HTTPPath = parts[1]
	}

	// 解析头部
	parseHTTPHeaders(packet, lines[1:])

	// 从Host头部获取域名
	if host, exists := packet.HTTPHeaders["Host"]; exists {
		packet.HTTPHost = host
	}

	log.Printf("🌐 解析HTTP请求: %s %s, Host: %s", packet.HTTPMethod, packet.HTTPPath, packet.HTTPHost)
}

// parseHTTPResponse 解析HTTP响应
func parseHTTPResponse(packet *Packet, lines []string) {
	if len(lines) == 0 {
		return
	}

	// 解析状态行: HTTP/1.1 200 OK
	statusLine := lines[0]
	packet.HTTPMethod = "RESPONSE"
	packet.HTTPPath = statusLine

	// 解析头部
	parseHTTPHeaders(packet, lines[1:])

	log.Printf("📡 解析HTTP响应: %s", statusLine)
}

// parseHTTPHeaders 解析HTTP头部
func parseHTTPHeaders(packet *Packet, lines []string) {
	for _, line := range lines {
		if line == "" {
			break // 空行表示头部结束
		}

		if colonIndex := strings.Index(line, ":"); colonIndex > 0 {
			key := strings.TrimSpace(line[:colonIndex])
			value := strings.TrimSpace(line[colonIndex+1:])
			packet.HTTPHeaders[key] = value

			// 特殊处理Host头部
			if strings.ToLower(key) == "host" {
				packet.HTTPHost = value
			}
		}
	}
}

// parseTLS 解析TLS握手，提取SNI
func parseTLS(packet *Packet, payload string) {
	data := []byte(payload)
	if len(data) < 6 {
		return
	}

	// 检查是否是TLS握手记录
	if data[0] == 0x16 { // TLS Handshake
		// 检查是否是Client Hello
		if len(data) > 5 && data[5] == 0x01 {
			sni := extractSNI(data)
			if sni != "" {
				packet.TLSServerName = sni
				packet.HTTPHost = sni // 也设置HTTPHost以便匹配
				log.Printf("🔒 解析TLS SNI: %s", sni)
			}
		}
	}
}

// extractSNI 从TLS Client Hello中提取SNI
func extractSNI(data []byte) string {
	if len(data) < 43 {
		return ""
	}

	// 跳过TLS记录头部(5字节) + 握手头部(4字节) + 版本(2字节) + 随机数(32字节)
	offset := 43

	// 跳过会话ID
	if offset >= len(data) {
		return ""
	}
	sessionIdLen := int(data[offset])
	offset += 1 + sessionIdLen

	// 跳过密码套件
	if offset+2 >= len(data) {
		return ""
	}
	cipherSuitesLen := int(data[offset])<<8 | int(data[offset+1])
	offset += 2 + cipherSuitesLen

	// 跳过压缩方法
	if offset >= len(data) {
		return ""
	}
	compressionMethodsLen := int(data[offset])
	offset += 1 + compressionMethodsLen

	// 解析扩展
	if offset+2 >= len(data) {
		return ""
	}
	extensionsLen := int(data[offset])<<8 | int(data[offset+1])
	offset += 2

	endOffset := offset + extensionsLen
	for offset < endOffset && offset+4 < len(data) {
		extType := int(data[offset])<<8 | int(data[offset+1])
		extLen := int(data[offset+2])<<8 | int(data[offset+3])
		offset += 4

		if extType == 0 { // SNI扩展
			return parseSNIExtension(data[offset : offset+extLen])
		}

		offset += extLen
	}

	return ""
}

// parseSNIExtension 解析SNI扩展
func parseSNIExtension(data []byte) string {
	if len(data) < 5 {
		return ""
	}

	// 跳过服务器名称列表长度(2字节)
	offset := 2

	for offset < len(data) {
		if offset+3 >= len(data) {
			break
		}

		nameType := data[offset]
		nameLen := int(data[offset+1])<<8 | int(data[offset+2])
		offset += 3

		if nameType == 0 && offset+nameLen <= len(data) { // 主机名
			return string(data[offset : offset+nameLen])
		}

		offset += nameLen
	}

	return ""
}

// parseDNS 解析DNS查询
func parseDNS(packet *Packet, payload string) {
	data := []byte(payload)
	if len(data) < 12 {
		return
	}

	// DNS头部：ID(2) + Flags(2) + QDCOUNT(2) + ANCOUNT(2) + NSCOUNT(2) + ARCOUNT(2)
	qdcount := int(data[4])<<8 | int(data[5])
	if qdcount == 0 {
		return
	}

	offset := 12 // 跳过DNS头部
	queries := make([]string, 0)

	for i := 0; i < qdcount && offset < len(data); i++ {
		domain := parseDNSName(data, offset)
		if domain != "" {
			queries = append(queries, domain)
			log.Printf("🌍 解析DNS查询: %s", domain)
		}

		// 跳过域名和查询类型/类别
		for offset < len(data) && data[offset] != 0 {
			labelLen := int(data[offset])
			if labelLen > 63 || offset+labelLen >= len(data) {
				break
			}
			offset += labelLen + 1
		}
		offset += 5 // 跳过结束符(1) + 类型(2) + 类别(2)
	}

	packet.DNSQueries = queries
}

// parseDNSName 解析DNS域名
func parseDNSName(data []byte, offset int) string {
	var parts []string

	for offset < len(data) && data[offset] != 0 {
		labelLen := int(data[offset])
		if labelLen == 0 {
			break
		}

		// 检查是否是压缩指针
		if labelLen&0xC0 == 0xC0 {
			// 压缩指针，跳过
			break
		}

		if labelLen > 63 || offset+labelLen >= len(data) {
			break
		}

		offset++
		if offset+labelLen > len(data) {
			break
		}

		label := string(data[offset : offset+labelLen])
		parts = append(parts, label)
		offset += labelLen
	}

	if len(parts) > 0 {
		return strings.Join(parts, ".")
	}

	return ""
}
