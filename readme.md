# NetInterceptor

一个基于 Go + WinDivert 的本机网络流量拦截工具，支持关键词规则匹配并将匹配内容写入 SQLite 数据库。使用 GORM 进行数据库操作，提供更好的类型安全和开发体验。

## ✨ 功能特性

* **全协议支持**：拦截TCP和UDP流量，支持HTTP、HTTPS、DNS等协议
* **智能HTTP协议解析**：自动识别和解析HTTP请求/响应
* **HTTPS支持**：通过TLS SNI（Server Name Indication）提取HTTPS域名
* **DNS查询监控**：捕获和解析DNS查询中的域名信息
* **多字段智能匹配**：
  - HTTP Host头部匹配
  - HTTPS TLS SNI匹配
  - DNS查询域名匹配
  - HTTP路径和头部匹配
  - 原始payload匹配
* 自定义关键词规则进行匹配
* 将符合条件的流量数据写入 SQLite
* 可配置是否放行或拦截流量（默认放行）
* 模块化设计，便于扩展协议解析或前端 UI
* 使用 GORM ORM 框架，提供类型安全的数据库操作
* **详细的调试日志**：显示各协议详情和匹配结果

## 📁 项目结构

```
net-interceptor/
├── main.go                    # 启动入口
├── interceptor/
│   └── windivert.go           # WinDivert 拦截器
├── parser/
│   └── parser.go              # TCP/IP/Payload 解析
├── matcher/
│   └── matcher.go             # 匹配规则逻辑
├── model/
│   └── sqlite.go              # GORM 数据模型和数据库操作
├── config/
│   └── rules.json             # 匹配关键词配置
├── README.md
└── go.mod
```

## 🚀 快速开始

### 1. 安装依赖

需要安装：

* Go 1.18+
* WinDivert 驱动（[官方下载地址](https://reqrypt.org/windivert.html)

```bash
go mod tidy
```

### 2. 编辑规则

在 `config/rules.json` 文件中写入需要拦截的关键词，例如：

```json
{
  "keywords": ["baidu.com", "password", "token"]
}
```

### 3. 编译并运行（管理员权限）

```bash
go build -o net-interceptor.exe .
```

#### 方法一：使用批处理文件（推荐）

双击运行 `check_admin.bat` 文件，它会自动检查管理员权限并启动程序。

#### 方法二：手动以管理员权限运行

1. 右键点击 `net-interceptor.exe`
2. 选择"以管理员身份运行"

#### 方法三：命令行运行

在管理员权限的命令提示符中运行：

```bash
./net-interceptor.exe
```

⚠️ **重要提示**：程序必须以管理员权限运行，否则 WinDivert 无法访问网络驱动。

### 4. 故障排除

#### 常见错误及解决方法

**错误码 5 (拒绝访问)**
- 原因：未以管理员权限运行
- 解决：右键程序选择"以管理员身份运行"

**错误码 2 (文件未找到)**
- 原因：WinDivert.dll 或 WinDivert64.sys 文件缺失
- 解决：确保项目根目录包含这些文件

**错误码 1275 (驱动程序失败)**
- 原因：WinDivert驱动不兼容当前系统
- 解决：下载适合当前系统版本的WinDivert

**编译错误**
- 确保安装了 Go 1.18+ 版本
- 运行 `go mod tidy` 安装依赖

### 5. 测试和验证

#### 生成测试流量

**方法一：使用内置HTTP测试工具**
```bash
.\test_http.exe
```

**方法二：使用批处理脚本**
```bash
.\test_network.bat
```

**方法三：手动测试**
```bash
curl -H "Host: ad.oceanengine.com" "http://ad.oceanengine.com/"
```

#### 检查捕获结果

**查看数据库记录**
```bash
.\check_db.exe
```

**观察拦截器日志**
拦截器会显示详细的调试信息：
- `🔍 解析到数据包` - 基本数据包信息和协议类型
- `🌐 HTTP请求` - HTTP请求详情（方法、路径）
- `🏠 Host` - HTTP Host头部
- `🔒 HTTPS SNI` - TLS握手中的服务器名称
- `🌍 DNS查询` - DNS查询的域名列表
- `🎯 匹配成功` - 规则匹配结果（显示匹配的字段和关键词）
- `✅ 命中规则` - 最终匹配确认
- `💾 数据包已保存` - 数据库保存确认

---

## 🧠 技术细节

### 拦截原理

使用 WinDivert 进行网络包捕获，支持过滤表达式：

```
tcp or udp
```

**支持的协议和捕获方式：**

1. **HTTP (端口80)**：解析HTTP请求头部，提取Host、路径等信息
2. **HTTPS (端口443)**：解析TLS握手中的SNI，获取目标域名
3. **DNS (端口53)**：解析DNS查询请求，获取查询的域名
4. **其他TCP/UDP流量**：捕获原始数据包内容

捕获的原始数据包通过智能解析器进行协议识别和解码，然后按规则匹配并写入 SQLite。

### 数据库存储结构

使用 GORM 自动管理数据库表结构，对应的 Go 结构体：

```go
type Packet struct {
    Id        int64     `gorm:"column:id;primaryKey;autoIncrement;comment:数据库主键ID"`
    SrcIp     string    `gorm:"column:src_ip;comment:源IP地址"`
    DstIp     string    `gorm:"column:dst_ip;comment:目标IP地址"`
    SrcPort   uint16    `gorm:"column:src_port;comment:源端口"`
    DstPort   uint16    `gorm:"column:dst_port;comment:目标端口"`
    Payload   string    `gorm:"column:payload;comment:数据包内容"`
    Timestamp time.Time `gorm:"column:timestamp;default:CURRENT_TIMESTAMP;comment:时间戳"`
}
```

---

## 📦 依赖组件

* [WinDivert](https://reqrypt.org/windivert.html)：网络包拦截驱动
* [GORM](https://gorm.io/)：Go ORM 框架
* [gorm.io/driver/sqlite](https://github.com/go-gorm/sqlite)：GORM SQLite 驱动

---

## ✅ TODO（未来可扩展）

* 支持入站流量拦截
* 增加 Web 前端查看流量记录
* 增加规则热更新功能
* 支持二进制 Payload 分析（如图片/文件）
* 增加协议层识别（如 HTTP/HTTPS/DNS）

---

## 📜 License

MIT License.
