package main

import (
	"fmt"
	"log"

	"net-interceptor/interceptor"
	"net-interceptor/matcher"
	"net-interceptor/model"
	"net-interceptor/parser"
)

func main() {
	// 初始化数据库
	db, err := model.InitDB("packets.db")
	if err != nil {
		log.Fatalf("数据库初始化失败: %v", err)
	}

	// 创建数据包模型
	packetModel := model.NewPacketModel(db)

	// 加载匹配规则
	rules, err := matcher.LoadRules("config/rules.json")
	if err != nil {
		log.Fatalf("加载规则失败: %v", err)
	}

	// 启动网络拦截器
	interceptor.Start(func(raw []byte, meta *interceptor.PacketMetadata) {
		// 解析数据包
		parserPacket := parser.Parse(meta, raw)
		if parserPacket == nil {
			return
		}
		fmt.Println("解析数据包", len(parserPacket.Payload))
		// 添加调试日志
		if len(parserPacket.Payload) > 0 {
			log.Printf("🔍 解析到数据包: %s:%d -> %s:%d, 协议: %s",
				parserPacket.SrcIP, parserPacket.SrcPort,
				parserPacket.DstIP, parserPacket.DstPort,
				parserPacket.Protocol)

			// 根据协议类型显示详细信息
			switch parserPacket.Protocol {
			case "HTTP":
				if parserPacket.HTTPMethod != "" && parserPacket.HTTPMethod != "RESPONSE" {
					log.Printf("🌐 HTTP请求: %s %s", parserPacket.HTTPMethod, parserPacket.HTTPPath)
				}
				if parserPacket.HTTPHost != "" {
					log.Printf("🏠 Host: %s", parserPacket.HTTPHost)
				}
			case "HTTPS":
				if parserPacket.TLSServerName != "" {
					log.Printf("🔒 HTTPS SNI: %s", parserPacket.TLSServerName)
				}
			case "DNS":
				if len(parserPacket.DNSQueries) > 0 {
					log.Printf("🌍 DNS查询: %v", parserPacket.DNSQueries)
				}
			default:
				if parserPacket.Payload != "[binary]" && len(parserPacket.Payload) > 0 {
					// 如果是文本内容，显示前100个字符
					preview := parserPacket.Payload
					if len(preview) > 100 {
						preview = preview[:100] + "..."
					}
					log.Printf("📄 Payload预览: %s", preview)
				}
			}
		}

		// 匹配规则 - 检查多个字段
		matchResult := matcher.MatchPacket(parserPacket, rules)
		if matchResult.Matched {
			log.Printf("✅ 命中规则：%s:%d -> %s:%d, 匹配字段: %s, 关键词: %s",
				parserPacket.SrcIP, parserPacket.SrcPort,
				parserPacket.DstIP, parserPacket.DstPort,
				matchResult.MatchedField, matchResult.MatchedKeyword)

			// 转换为模型结构，包含更多信息
			enrichedPayload := parserPacket.Payload
			switch parserPacket.Protocol {
			case "HTTP":
				if parserPacket.HTTPHost != "" {
					enrichedPayload = fmt.Sprintf("HTTP %s %s | Host: %s | %s",
						parserPacket.HTTPMethod, parserPacket.HTTPPath,
						parserPacket.HTTPHost, parserPacket.Payload)
				}
			case "HTTPS":
				if parserPacket.TLSServerName != "" {
					enrichedPayload = fmt.Sprintf("HTTPS TLS SNI: %s | %s",
						parserPacket.TLSServerName, parserPacket.Payload)
				}
			case "DNS":
				if len(parserPacket.DNSQueries) > 0 {
					enrichedPayload = fmt.Sprintf("DNS查询: %v | %s",
						parserPacket.DNSQueries, parserPacket.Payload)
				}
			}

			packet := &model.Packet{
				SrcIp:   parserPacket.SrcIP,
				DstIp:   parserPacket.DstIP,
				SrcPort: parserPacket.SrcPort,
				DstPort: parserPacket.DstPort,
				Payload: enrichedPayload,
			}

			if err := packetModel.Create(packet); err != nil {
				log.Printf("❌ 保存数据包失败: %v", err)
			} else {
				log.Printf("💾 数据包已保存到数据库")
			}
		}
	})
}
