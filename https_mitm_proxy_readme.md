
# HTTPS 解密代理系统：WinDivert + GoProxy + 自签名证书

本项目目标是构建一个**透明 HTTPS 抓包系统**，实现对本机或设备的 HTTPS 请求进行明文解析与存储。

---

## 🧠 项目目标

实现以下流程：

1. 使用 WinDivert 拦截出站流量（尤其是 443 端口）
2. 判断是否为 HTTPS 请求
3. 将请求重定向到本地运行的 GoProxy（监听端口 8888）
4. 使用自签名根证书伪造目标网站证书
5. 解密 HTTPS 内容，输出或存入数据库

---

## 🔧 技术组件

| 技术 | 用途 |
|------|------|
| WinDivert | 驱动层拦截所有 TCP/UDP 包 |
| Go + goproxy | 构建支持 MITM 的 HTTPS 代理服务器 |
| OpenSSL | 用于生成自签名根证书 |
| SQLite | 可选，保存解析出的请求信息 |

---

## 🔐 HTTPS 是如何被解密的？

通过**MITM（中间人）技术**实现：

```
客户端 <==TLS==> 伪造服务器（你的程序） <==TLS==> 真实服务器
```

你伪造了一个可信的证书（自签根证书 + 伪造网站证书），从而能拦截并解密客户端发出的 HTTPS 内容。

---

## 📝 自签名证书生成步骤

### 使用 OpenSSL 命令

```bash
# 1. 生成私钥
openssl genrsa -out rootCA.key 2048

# 2. 生成根证书（10年有效期）
openssl req -x509 -new -nodes -key rootCA.key -sha256 -days 3650 -out rootCA.pem -subj "/C=CN/ST=Test/L=Proxy/O=YourProxy/CN=YourProxy Root CA"
```

生成两个文件：

- `rootCA.pem`：根证书（安装给浏览器/手机）
- `rootCA.key`：根私钥（用来伪造证书）

### 安装证书到 Windows 系统

1. 双击 `rootCA.pem`
2. 点击“安装证书”
3. 选择“本地计算机”
4. 选择“受信任的根证书颁发机构”

---

## 🚀 GoProxy 示例代码

```go
package main

import (
    "log"
    "net/http"
    "crypto/tls"

    "github.com/elazarl/goproxy"
)

func main() {
    proxy := goproxy.NewProxyHttpServer()
    proxy.Verbose = true

    cert, err := tls.LoadX509KeyPair("rootCA.pem", "rootCA.key")
    if err != nil {
        log.Fatal("加载证书失败:", err)
    }
    goproxy.GoproxyCa = &cert

    proxy.OnRequest().HandleConnect(goproxy.AlwaysMitm)
    log.Println("HTTPS MITM 代理已启动: http://127.0.0.1:8888")
    log.Fatal(http.ListenAndServe(":8888", proxy))
}
```

---

## 🕸️ WinDivert + GoProxy 协同架构

1. 使用 WinDivert 拦截 `tcp.DstPort == 443` 的请求包
2. 将目标地址修改为 `127.0.0.1:8888`（重定向到 goproxy）
3. goproxy 使用伪造证书解密请求，记录明文
4. 可选：使用 SQLite 存储信息

---

## ✅ 推荐的流程组合

| 步骤 | 工具 |
|------|------|
| 全局流量拦截 | WinDivert |
| HTTPS 解密 | GoProxy + 自签证书 |
| 请求记录 | GoProxy 回调中记录 |
| 数据存储 | SQLite / 文件 / 控制台 |

---

## 📁 目录结构建议

```
project-root/
├── main.go               // goproxy 启动逻辑
├── rootCA.pem            // 根证书（安装给客户端）
├── rootCA.key            // 根私钥（签发伪造证书）
└── README.md             // 本文档
```

---

## ✅ 最终目标

实现一个类似于 Charles、Fiddler 的 HTTPS 明文抓包代理系统，支持：

- 抓取 HTTPS 请求和响应的明文内容
- 可编程处理数据（如存入数据库）
- 可与 WinDivert 搭配做全局透明代理

---

## ❓ FAQ

### Q: 一定要安装根证书吗？  
是的。否则客户端（浏览器/APP）会显示“连接不安全”或直接拒绝连接。

### Q: 自签证书安全吗？  
用于本地开发测试没有问题，但绝不能用于生产环境。

---

## ✨ 补充建议

- 手机抓包也可以使用：将手机代理设置为你的本地地址（如 192.168.0.101:8888）并信任根证书；
- 更高级的 HTTPS 检测可以从 TLS ClientHello 中提取 SNI 信息；
- 可结合 WinDivert 的 `tcp and outbound` 过滤器实现精细控制。

---

🛠 有需要我可以为你生成完整的代码工程模板。
